# General <PERSON> Development
- In <PERSON>, all functions must be declared before they are referenced.
- The user prefers input parameters in scripts to be organized with most important and frequently used settings at the top of each group, secondary settings in the middle, and less common or advanced settings toward the bottom, with related settings grouped together and clear section comments.
- The strategy works on the TradingView platform, which doesn't support checking minutes for time-based features.
- In Pine Script, function arguments cannot be mutable, so we need to return modified arrays from functions rather than trying to modify them directly.
- In Pine Script, the correct return syntax is to use the arrow operator (=>) without an explicit 'return' keyword.
- Pine Script does not have an 'exists' function for checking if variables are defined.
- In Pine Script, when defining custom types like 'OrderBlock', you must use the 'type' keyword before the type name (e.g., 'type OrderBlock' not just 'OrderBlock').
- In Pine Script, all custom functions, variables, and type definitions must be declared before they are referenced or called - function declaration order matters for compilation.
- In Pine Script, all custom functions must be declared before they are referenced, and this applies to the entire call chain of dependencies - when moving functions to fix declaration order, also move any functions they depend on to maintain proper ordering.
- In <PERSON>, circular dependencies between functions can occur when functions reference variables that are defined after them, requiring alternative approaches beyond simple function reordering to resolve compilation issues.