Perfect! Here's the complete recommended approach with selectable liquidity styles:

```pinescript
// ====================================================================
// ENHANCED LIQUIDITY VISUALIZATION SYSTEM
// ====================================================================

// Add these enhanced input parameters (replace the existing liquidity inputs)
group_liquidity = "💧 Liquidity Pool Detection"
enableLiquidityPools = input.bool(false, "💧 Enable Liquidity Pools", group=group_liquidity, tooltip="Detect areas where stop losses likely cluster")
liquidityLookback = input.int(50, "🔍 Liquidity Lookback", minval=20, maxval=200, group=group_liquidity, tooltip="Bars to look back for liquidity pools")
liquidityMinAge = input.int(10, "⏰ Min Pool Age", minval=5, maxval=50, group=group_liquidity, tooltip="Minimum bars since high/low for valid liquidity")
liquidityStrengthBonus = input.float(2.0, "💪 Strength Bonus", minval=1.0, maxval=5.0, step=0.5, group=group_liquidity, tooltip="Bonus multiplier for liquidity pool zones")
liquidityRetestCount = input.int(2, "🔄 Min Retests", minval=0, maxval=5, group=group_liquidity, tooltip="Minimum times level should be tested")

// Enhanced liquidity visualization options
liquidityStyle = input.string("Zones", "💧 Liquidity Style", options=["Markers", "Zones", "Lines", "Heatmap"], group=group_liquidity, tooltip="Choose how to display liquidity areas")
showLiquiditySweeps = input.bool(true, "🚀 Show Liquidity Sweeps", group=group_liquidity, tooltip="Alert when liquidity levels are swept")
liquidityMinStrength = input.float(2.0, "💪 Min Strength", minval=1.0, maxval=5.0, step=0.5, group=group_liquidity, tooltip="Minimum strength to display liquidity level")
maxLiquidityLevels = input.int(5, "📊 Max Levels", minval=3, maxval=10, group=group_liquidity, tooltip="Maximum liquidity levels to track")
heatmapBars = input.int(100, "📊 Heatmap Bars", minval=50, maxval=200, group=group_liquidity, tooltip="Number of bars for heatmap calculation")
liquidityExtension = input.int(25, "➡️ Extend Liquidity", minval=10, maxval=50, group=group_liquidity, tooltip="How far to extend liquidity lines/zones")

// ====================================================================
// ENHANCED LIQUIDITY TYPE DEFINITIONS
// ====================================================================

// Enhanced LiquidityLevel type
type LiquidityLevel
    float level
    int tests
    bool is_high
    int created_bar
    bool is_swept
    float strength
    float total_volume
    box zone_box
    line level_line
    label level_label

// ====================================================================
// ENHANCED LIQUIDITY FUNCTIONS
// ====================================================================

// Create liquidity zone visualization
f_createLiquidityZone(level, tests, isHigh, strength) =>
    // Calculate zone thickness based on tests and strength
    float zone_thickness = ta.atr(14) * (0.15 + (tests * 0.05) + (strength * 0.03))
    
    // Color intensity based on liquidity strength
    int transparency = math.max(50, 85 - (tests * 3) - int(strength * 8))
    
    color zone_color = isHigh ? 
         color.new(color.red, transparency) : 
         color.new(color.new(#2196F3), transparency)  // Nice blue
    
    color border_color = isHigh ? 
         color.new(color.red, 30) : 
         color.new(color.new(#2196F3), 30)
    
    // Create liquidity zone box
    upper_level = level + (zone_thickness / 2)
    lower_level = level - (zone_thickness / 2)
    
    liq_box = box.new(
         bar_index - 30, upper_level,
         bar_index + liquidityExtension, lower_level,
         bgcolor=zone_color,
         border_color=border_color,
         border_width=math.min(3, 1 + tests),
         border_style=line.style_dashed,
         extend=extend.right
         )
    
    liq_box

// Create liquidity line visualization
f_createLiquidityLine(level, tests, isHigh, strength) =>
    color line_color = isHigh ? 
         color.new(color.red, 20) : 
         color.new(color.new(#2196F3), 20)
    
    int line_width = math.min(5, 1 + tests)
    
    liq_line = line.new(
         bar_index - 30, level,
         bar_index + liquidityExtension, level,
         color=line_color,
         width=line_width,
         style=line.style_dashed,
         extend=extend.right
         )
    
    liq_line

// Create liquidity marker
f_createLiquidityMarker(level, tests, isHigh, strength) =>
    strength_pct = math.round(strength * 20, 0)  // Convert to percentage
    
    marker_text = "💧 " + (isHigh ? "SELL" : "BUY") + " LIQ\n" +
                  "🎯 " + str.tostring(tests) + " Tests\n" + 
                  "💪 " + str.tostring(strength_pct) + "% Strength"
    
    marker_color = isHigh ? 
         color.new(color.red, 60) : 
         color.new(color.new(#2196F3), 60)
    
    marker_style = isHigh ? 
         label.style_label_down : 
         label.style_label_up
    
    liq_label = label.new(
         bar_index, level,
         marker_text,
         style=marker_style,
         color=marker_color,
         textcolor=color.white,
         size=size.small
         )
    
    liq_label

// Detect liquidity sweep
f_detectLiquiditySweep(level, isHigh, tolerance) =>
    bool swept = false
    bool high_volume = volume > ta.sma(volume, 20) * 1.3
    
    if isHigh
        // Check if high was taken out with volume
        if high >= level * (1 + tolerance) and high_volume
            swept := true
    else
        // Check if low was taken out with volume
        if low <= level * (1 - tolerance) and high_volume
            swept := true
    swept

// Create liquidity heatmap
f_createLiquidityHeatmap() =>
    // Divide price range into zones
    price_range = ta.highest(high, heatmapBars) - ta.lowest(low, heatmapBars)
    zone_count = 15  // 15 zones for better granularity
    zone_size = price_range / zone_count
    base_price = ta.lowest(low, heatmapBars)
    
    // Count volume-weighted touches in each zone
    var float[] zone_liquidity = array.new_float(0)
    array.clear(zone_liquidity)
    
    // Initialize zones
    for i = 0 to zone_count - 1
        array.push(zone_liquidity, 0.0)
    
    // Calculate liquidity for each zone
    for i = 0 to math.min(heatmapBars - 1, 200)  // Limit for performance
        bar_high = high[i]
        bar_low = low[i]
        bar_volume = volume[i]
        
        // Add volume to zones that price touched
        high_zone = math.floor((bar_high - base_price) / zone_size)
        low_zone = math.floor((bar_low - base_price) / zone_size)
        
        // Weight by how many times the level was tested
        weight_multiplier = 1.0
        for j = i + 1 to math.min(i + 10, heatmapBars - 1)
            if math.abs(high[j] - bar_high) < zone_size * 0.5 or 
               math.abs(low[j] - bar_low) < zone_size * 0.5
                weight_multiplier += 0.2
        
        if high_zone >= 0 and high_zone < zone_count
            current_liq = array.get(zone_liquidity, int(high_zone))
            array.set(zone_liquidity, int(high_zone), current_liq + (bar_volume * weight_multiplier))
        
        if low_zone >= 0 and low_zone < zone_count and low_zone != high_zone
            current_liq = array.get(zone_liquidity, int(low_zone))
            array.set(zone_liquidity, int(low_zone), current_liq + (bar_volume * weight_multiplier))
    
    // Find max liquidity for normalization
    max_liquidity = 0.0
    for i = 0 to zone_count - 1
        liquidity = array.get(zone_liquidity, i)
        if liquidity > max_liquidity
            max_liquidity := liquidity
    
    // Draw heatmap zones
    if max_liquidity > 0
        for i = 0 to zone_count - 1
            zone_bottom = base_price + (i * zone_size)
            zone_top = zone_bottom + zone_size
            liquidity = array.get(zone_liquidity, i)
            intensity = liquidity / max_liquidity
            
            if intensity > 0.2  // Only show significant zones
                transparency = int(85 - (intensity * 50))
                
                zone_color = intensity > 0.8 ? color.new(color.red, transparency) :      // Highest liquidity
                            intensity > 0.6 ? color.new(color.orange, transparency) :   // High liquidity  
                            intensity > 0.4 ? color.new(color.yellow, transparency) :   // Medium liquidity
                            color.new(color.blue, transparency)                         // Lower liquidity
                
                box.new(
                    bar_index - 20, zone_top, 
                    bar_index + liquidityExtension, zone_bottom,
                    bgcolor=zone_color,
                    border_color=color.new(color.gray, 70),
                    border_width=1,
                    extend=extend.right
                    )
                
                // Add intensity label for high liquidity zones
                if intensity > 0.6
                    label.new(
                        bar_index + liquidityExtension - 5, (zone_top + zone_bottom) / 2,
                        str.tostring(math.round(intensity * 100, 0)) + "%",
                        style=label.style_label_left,
                        color=color.new(color.black, 20),
                        textcolor=color.white,
                        size=size.tiny
                        )

// Enhanced liquidity pool detection that tracks multiple levels
f_updateLiquidityLevels() =>
    // Clean old levels and update existing ones
    if array.size(liquidityLevels) > 0
        for i = array.size(liquidityLevels) - 1 to 0
            liq = array.get(liquidityLevels, i)
            
            // Remove very old levels
            if bar_index - liq.created_bar > 300
                // Clean up visual elements
                if not na(liq.zone_box)
                    box.delete(liq.zone_box)
                if not na(liq.level_line)
                    line.delete(liq.level_line)
                if not na(liq.level_label)
                    label.delete(liq.level_label)
                array.remove(liquidityLevels, i)
                continue
            
            // Check for sweeps
            if not liq.is_swept and showLiquiditySweeps
                if f_detectLiquiditySweep(liq.level, liq.is_high, 0.001)
                    liq.is_swept := true
                    
                    // Create sweep alert
                    sweep_text = "🚀 LIQUIDITY SWEPT!\n" + 
                                (liq.is_high ? "Sell stops triggered" : "Buy stops triggered") +
                                "\n🎯 " + str.tostring(liq.tests) + " tests cleared"
                    
                    label.new(
                        bar_index, liq.is_high ? high : low,
                        sweep_text,
                        style=liq.is_high ? label.style_arrowdown : label.style_arrowup,
                        color=color.new(color.orange, 10),
                        textcolor=color.white,
                        size=size.normal
                        )
    
    // Find new liquidity levels
    lookback = liquidityLookback
    min_tests = liquidityRetestCount
    
    for i = liquidityMinAge to math.min(lookback, 100)  // Limit for performance
        is_significant_high = high[i] == ta.highest(high, math.min(i + 5, lookback))
        is_significant_low = low[i] == ta.lowest(low, math.min(i + 5, lookback))
        
        // Check for significant highs
        if is_significant_high
            test_count = 1
            total_volume = volume[i]
            
            // Count retests
            for j = 0 to i - 1
                if math.abs(high[j] - high[i]) < ta.atr(14) * 0.1
                    test_count += 1
                    total_volume += volume[j]
            
            if test_count >= min_tests
                // Check if level already exists
                level_exists = false
                if array.size(liquidityLevels) > 0
                    for k = 0 to array.size(liquidityLevels) - 1
                        existing = array.get(liquidityLevels, k)
                        if existing.is_high and math.abs(existing.level - high[i]) < ta.atr(14) * 0.2
                            // Update existing level
                            existing.tests := math.max(existing.tests, test_count)
                            existing.total_volume += total_volume
                            existing.strength := (existing.tests / 5.0) + (existing.total_volume / ta.sma(volume, 50))
                            level_exists := true
                            break
                
                if not level_exists and array.size(liquidityLevels) < maxLiquidityLevels
                    strength = (test_count / 5.0) + (total_volume / ta.sma(volume, 50))
                    if strength >= liquidityMinStrength
                        new_level = LiquidityLevel.new(
                             high[i], test_count, true, bar_index - i, false, 
                             strength, total_volume, na, na, na)
                        array.push(liquidityLevels, new_level)
        
        // Check for significant lows (similar logic)
        if is_significant_low
            test_count = 1
            total_volume = volume[i]
            
            for j = 0 to i - 1
                if math.abs(low[j] - low[i]) < ta.atr(14) * 0.1
                    test_count += 1
                    total_volume += volume[j]
            
            if test_count >= min_tests
                level_exists = false
                if array.size(liquidityLevels) > 0
                    for k = 0 to array.size(liquidityLevels) - 1
                        existing = array.get(liquidityLevels, k)
                        if not existing.is_high and math.abs(existing.level - low[i]) < ta.atr(14) * 0.2
                            existing.tests := math.max(existing.tests, test_count)
                            existing.total_volume += total_volume
                            existing.strength := (existing.tests / 5.0) + (existing.total_volume / ta.sma(volume, 50))
                            level_exists := true
                            break
                
                if not level_exists and array.size(liquidityLevels) < maxLiquidityLevels
                    strength = (test_count / 5.0) + (total_volume / ta.sma(volume, 50))
                    if strength >= liquidityMinStrength
                        new_level = LiquidityLevel.new(
                             low[i], test_count, false, bar_index - i, false, 
                             strength, total_volume, na, na, na)
                        array.push(liquidityLevels, new_level)

// Display liquidity levels based on selected style
f_displayLiquidityLevels() =>
    if array.size(liquidityLevels) > 0
        for liq in liquidityLevels
            if not liq.is_swept and liq.strength >= liquidityMinStrength
                
                // Clean up old visual elements
                if not na(liq.zone_box)
                    box.delete(liq.zone_box)
                if not na(liq.level_line)
                    line.delete(liq.level_line)
                if not na(liq.level_label)
                    label.delete(liq.level_label)
                
                // Create new visuals based on style
                if liquidityStyle == "Zones"
                    liq.zone_box := f_createLiquidityZone(liq.level, liq.tests, liq.is_high, liq.strength)
                    
                else if liquidityStyle == "Lines"
                    liq.level_line := f_createLiquidityLine(liq.level, liq.tests, liq.is_high, liq.strength)
                    
                else if liquidityStyle == "Markers"
                    liq.level_label := f_createLiquidityMarker(liq.level, liq.tests, liq.is_high, liq.strength)

// ====================================================================
// ADD THESE GLOBAL VARIABLES (with the other var declarations)
// ====================================================================

// Enhanced liquidity tracking
var liquidityLevels = array.new<LiquidityLevel>()

// ====================================================================
// REPLACE THE LIQUIDITY SECTION IN YOUR MAIN LOGIC
// ====================================================================

// Enhanced Liquidity Pool Detection and Visualization
if enableLiquidityPools
    // Update and track multiple liquidity levels
    f_updateLiquidityLevels()
    
    // Display based on selected style
    if liquidityStyle == "Heatmap"
        f_createLiquidityHeatmap()
    else
        f_displayLiquidityLevels()
    
    // Legacy single level detection for backward compatibility
    [highValid, highLevel, highTests] = f_detectLiquidityPool(true, liquidityLookback, liquidityMinAge, liquidityRetestCount)
    [lowValid, lowLevel, lowTests] = f_detectLiquidityPool(false, liquidityLookback, liquidityMinAge, liquidityRetestCount)
    
    liquidityHighDetected := highValid
    liquidityLowDetected := lowValid
    liquidityHighLevel := highLevel
    liquidityLowLevel := lowLevel
    liquidityHighTests := highTests
    liquidityLowTests := lowTests
```

This enhanced system provides:

1. **Markers**: Simple labeled markers showing liquidity levels
2. **Zones**: Thick colored zones with varying transparency based on strength
3. **Lines**: Clean dashed lines with thickness based on test count
4. **Heatmap**: Volume-weighted density map showing liquidity concentration

Each style dynamically adjusts colors, transparency, and thickness based on:
- Number of tests at the level
- Volume concentration
- Strength calculation
- Time since creation

The system also includes liquidity sweep detection with visual alerts when levels are taken out with volume. You can now select any style from the dropdown and see how liquidity areas are visualized differently!